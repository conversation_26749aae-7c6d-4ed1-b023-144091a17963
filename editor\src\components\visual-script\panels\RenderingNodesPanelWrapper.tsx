/**
 * 核心渲染系统节点面板包装器
 * 集成批次1：用于集成80个核心渲染系统节点到编辑器的面板系统中
 */

import React, { useState, useEffect, useRef } from 'react';
import { Card, message, Spin, Alert } from 'antd';
import { BgColorsOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import RenderingNodesPanel, { RenderingNodeItem } from './RenderingNodesPanel';
import { 
  RenderingNodesIntegration, 
  integrateRenderingNodes 
} from '../nodes/RenderingNodesIntegration';

// 模拟节点编辑器接口
interface MockNodeEditor {
  addNodeToPalette?: (nodeType: string, nodeConfig: any) => void;
  addNodePalette?: (palette: any) => void;
  registerNode?: (nodeType: string, nodeClass: any) => void;
  createNode?: (nodeType: string, position?: { x: number; y: number }) => any;
  getRegisteredNodes?: () => Map<string, any>;
}

interface RenderingNodesPanelWrapperProps {
  onNodeSelect?: (nodeType: string, nodeConfig: any) => void;
  onNodeAdd?: (nodeType: string, position?: { x: number; y: number }) => void;
  height?: number | string;
  width?: number | string;
}

/**
 * 核心渲染系统节点面板包装器组件
 */
const RenderingNodesPanelWrapper: React.FC<RenderingNodesPanelWrapperProps> = ({
  onNodeSelect,
  onNodeAdd,
  height = '100%',
  width = '100%'
}) => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [integration, setIntegration] = useState<RenderingNodesIntegration | null>(null);
  const nodeEditorRef = useRef<MockNodeEditor | null>(null);

  /**
   * 创建模拟节点编辑器
   */
  const createMockNodeEditor = (): MockNodeEditor => {
    const registeredNodes = new Map();
    const nodePalettes: any[] = [];

    return {
      addNodeToPalette: (nodeType: string, nodeConfig: any) => {
        console.log(`添加节点到面板: ${nodeType}`, nodeConfig);
      },
      
      addNodePalette: (palette: any) => {
        nodePalettes.push(palette);
        console.log(`添加节点面板: ${palette.category}`, palette);
      },
      
      registerNode: (nodeType: string, nodeClass: any) => {
        registeredNodes.set(nodeType, nodeClass);
        console.log(`注册节点: ${nodeType}`);
      },
      
      createNode: (nodeType: string, position?: { x: number; y: number }) => {
        const NodeClass = registeredNodes.get(nodeType);
        if (NodeClass) {
          const node = new NodeClass();
          console.log(`创建节点: ${nodeType}`, position);
          return node;
        }
        console.warn(`未找到节点类型: ${nodeType}`);
        return null;
      },
      
      getRegisteredNodes: () => {
        return registeredNodes;
      }
    };
  };

  // 初始化核心渲染系统节点集成
  useEffect(() => {
    const initializeIntegration = async () => {
      try {
        setLoading(true);
        setError(null);

        // 创建模拟节点编辑器
        const mockNodeEditor = createMockNodeEditor();
        nodeEditorRef.current = mockNodeEditor;

        // 创建核心渲染系统节点集成
        const renderingIntegration = new RenderingNodesIntegration(mockNodeEditor);
        
        // 集成所有核心渲染系统节点
        renderingIntegration.integrateAllNodes();
        
        setIntegration(renderingIntegration);
        
        message.success(`核心渲染系统节点集成成功！已集成 ${renderingIntegration.getRegisteredNodeCount()} 个节点`);
        
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : '未知错误';
        setError(errorMessage);
        message.error(`核心渲染系统节点集成失败: ${errorMessage}`);
        console.error('核心渲染系统节点集成失败:', err);
      } finally {
        setLoading(false);
      }
    };

    initializeIntegration();
  }, []);

  // 处理节点选择
  const handleNodeSelect = (node: RenderingNodeItem) => {
    if (onNodeSelect) {
      const nodeConfig = {
        id: node.id,
        name: node.name,
        type: node.type,
        category: node.category,
        description: node.description,
        complexity: node.complexity,
        tags: node.tags,
        inputs: node.inputs,
        outputs: node.outputs,
        properties: node.properties
      };
      onNodeSelect(node.type, nodeConfig);
    }
  };

  // 处理节点添加
  const handleNodeAdd = (nodeType: string) => {
    if (nodeEditorRef.current?.createNode) {
      const position = { x: Math.random() * 400, y: Math.random() * 300 };
      const node = nodeEditorRef.current.createNode(nodeType, position);
      
      if (node && onNodeAdd) {
        onNodeAdd(nodeType, position);
        message.success(`已添加 ${nodeType} 节点到编辑器`);
      }
    }
  };

  // 如果有错误，显示错误信息
  if (error) {
    return (
      <Card
        title={
          <span>
            <BgColorsOutlined /> 核心渲染系统节点
          </span>
        }
        style={{ height, width }}
      >
        <Alert
          message="集成失败"
          description={error}
          type="error"
          showIcon
          action={
            <button 
              onClick={() => window.location.reload()}
              style={{ 
                border: 'none', 
                background: 'transparent', 
                color: '#1890ff', 
                cursor: 'pointer' 
              }}
            >
              重新加载
            </button>
          }
        />
      </Card>
    );
  }

  // 如果正在加载，显示加载状态
  if (loading) {
    return (
      <Card
        title={
          <span>
            <BgColorsOutlined /> 核心渲染系统节点
          </span>
        }
        style={{ height, width }}
      >
        <div style={{ 
          display: 'flex', 
          justifyContent: 'center', 
          alignItems: 'center', 
          height: '200px' 
        }}>
          <Spin size="large" tip="正在集成核心渲染系统节点..." />
        </div>
      </Card>
    );
  }

  // 渲染核心渲染系统节点面板
  return (
    <RenderingNodesPanel
      visible={true}
      onNodeSelect={handleNodeSelect}
      onNodeAdd={handleNodeAdd}
      height={height}
      width={width}
    />
  );
};

export default RenderingNodesPanelWrapper;

/**
 * 导出集成相关的类型和函数
 */
export { RenderingNodesIntegration, integrateRenderingNodes };
export type { RenderingNodeItem };
