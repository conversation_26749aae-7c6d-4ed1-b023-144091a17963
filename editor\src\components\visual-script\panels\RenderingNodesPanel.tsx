/**
 * 核心渲染系统节点面板组件
 * 集成批次1：显示和管理80个核心渲染系统节点，包括7个子分类
 */

import React, { useState, useEffect, useMemo, useCallback } from 'react';
import {
  Card,
  Collapse,
  List,
  Tag,
  Space,
  Button,
  Tooltip,
  Badge,
  Typography,
  Divider,
  Empty,
  Spin,
  Input,
  Select,
  Row,
  Col
} from 'antd';
import {
  BgColorsOutlined,
  CodeOutlined,
  CameraOutlined,
  ThunderboltOutlined,
  FilterOutlined,
  HighlightOutlined,
  InfoCircleOutlined,
  PlusOutlined,
  SearchOutlined,
  SettingOutlined,
  EyeOutlined,
  BuildOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { DraggableRenderingNode } from '../nodes/RenderingNodeDragDrop';

const { Panel } = Collapse;
const { Text, Title } = Typography;
const { Search } = Input;
const { Option } = Select;

// 渲染系统节点分类枚举
export enum RenderingNodeCategory {
  MATERIAL_EDITING = 'materialEditing',
  BASIC_SHADER = 'basicShader',
  ADVANCED_SHADER = 'advancedShader',
  LIGHTING_CAMERA = 'lightingCamera',
  RENDERING_OPTIMIZATION = 'renderingOptimization',
  BASIC_POST_PROCESSING = 'basicPostProcessing',
  ADVANCED_POST_PROCESSING = 'advancedPostProcessing'
}

// 渲染系统节点分类映射
export const RENDERING_CATEGORY_MAP = {
  [RenderingNodeCategory.MATERIAL_EDITING]: {
    name: '材质编辑',
    icon: <BgColorsOutlined />,
    color: '#ff6b6b',
    description: '材质创建、编辑和管理节点'
  },
  [RenderingNodeCategory.BASIC_SHADER]: {
    name: '基础着色器',
    icon: <CodeOutlined />,
    color: '#4ecdc4',
    description: '基础着色器编程节点'
  },
  [RenderingNodeCategory.ADVANCED_SHADER]: {
    name: '高级着色器',
    icon: <BuildOutlined />,
    color: '#45b7d1',
    description: '高级着色器功能节点'
  },
  [RenderingNodeCategory.LIGHTING_CAMERA]: {
    name: '光照相机',
    icon: <CameraOutlined />,
    color: '#f9ca24',
    description: '光照和相机控制节点'
  },
  [RenderingNodeCategory.RENDERING_OPTIMIZATION]: {
    name: '渲染优化',
    icon: <ThunderboltOutlined />,
    color: '#6c5ce7',
    description: '渲染性能优化节点'
  },
  [RenderingNodeCategory.BASIC_POST_PROCESSING]: {
    name: '基础后处理',
    icon: <FilterOutlined />,
    color: '#a29bfe',
    description: '基础后处理效果节点'
  },
  [RenderingNodeCategory.ADVANCED_POST_PROCESSING]: {
    name: '高级后处理',
    icon: <HighlightOutlined />,
    color: '#fd79a8',
    description: '高级后处理效果节点'
  }
};

// 节点接口定义
export interface RenderingNodeItem {
  id: string;
  name: string;
  type: string;
  category: RenderingNodeCategory;
  description: string;
  icon?: React.ReactNode;
  tags?: string[];
  complexity?: 'basic' | 'intermediate' | 'advanced';
  inputs?: Array<{ name: string; type: string; description: string }>;
  outputs?: Array<{ name: string; type: string; description: string }>;
  properties?: Array<{ name: string; type: string; defaultValue: any; description: string }>;
}

// 组件属性接口
interface RenderingNodesPanelProps {
  visible?: boolean;
  onNodeSelect?: (node: RenderingNodeItem) => void;
  onNodeAdd?: (nodeType: string) => void;
  height?: number | string;
  width?: number | string;
}

/**
 * 核心渲染系统节点面板组件
 */
const RenderingNodesPanel: React.FC<RenderingNodesPanelProps> = ({
  visible = true,
  onNodeSelect,
  onNodeAdd,
  height = '100%',
  width = '100%'
}) => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [expandedPanels, setExpandedPanels] = useState<string[]>(['materialEditing']);
  const [filteredNodes, setFilteredNodes] = useState<RenderingNodeItem[]>([]);

  // 材质编辑节点（14个）
  const materialEditingNodes: RenderingNodeItem[] = useMemo(() => [
    {
      id: 'material-system',
      name: '材质系统',
      type: 'MaterialSystem',
      category: RenderingNodeCategory.MATERIAL_EDITING,
      description: '管理材质系统的核心功能',
      complexity: 'basic',
      tags: ['系统', '管理']
    },
    {
      id: 'create-material',
      name: '创建材质',
      type: 'CreateMaterial',
      category: RenderingNodeCategory.MATERIAL_EDITING,
      description: '创建新的材质对象',
      complexity: 'basic',
      tags: ['创建', '材质']
    },
    {
      id: 'set-material-property',
      name: '设置材质属性',
      type: 'SetMaterialProperty',
      category: RenderingNodeCategory.MATERIAL_EDITING,
      description: '设置材质的属性值',
      complexity: 'basic',
      tags: ['设置', '属性']
    },
    {
      id: 'get-material-property',
      name: '获取材质属性',
      type: 'GetMaterialProperty',
      category: RenderingNodeCategory.MATERIAL_EDITING,
      description: '获取材质的属性值',
      complexity: 'basic',
      tags: ['获取', '属性']
    },
    {
      id: 'material-blend',
      name: '材质混合',
      type: 'MaterialBlend',
      category: RenderingNodeCategory.MATERIAL_EDITING,
      description: '混合多个材质',
      complexity: 'intermediate',
      tags: ['混合', '材质']
    },
    {
      id: 'material-animation',
      name: '材质动画',
      type: 'MaterialAnimation',
      category: RenderingNodeCategory.MATERIAL_EDITING,
      description: '创建材质动画效果',
      complexity: 'intermediate',
      tags: ['动画', '材质']
    },
    {
      id: 'material-optimization',
      name: '材质优化',
      type: 'MaterialOptimization',
      category: RenderingNodeCategory.MATERIAL_EDITING,
      description: '优化材质性能',
      complexity: 'advanced',
      tags: ['优化', '性能']
    },
    {
      id: 'pbr-material',
      name: 'PBR材质',
      type: 'PBRMaterial',
      category: RenderingNodeCategory.MATERIAL_EDITING,
      description: '创建物理基础渲染材质',
      complexity: 'intermediate',
      tags: ['PBR', '物理']
    },
    {
      id: 'standard-material',
      name: '标准材质',
      type: 'StandardMaterial',
      category: RenderingNodeCategory.MATERIAL_EDITING,
      description: '创建标准材质',
      complexity: 'basic',
      tags: ['标准', '材质']
    },
    {
      id: 'custom-material',
      name: '自定义材质',
      type: 'CustomMaterial',
      category: RenderingNodeCategory.MATERIAL_EDITING,
      description: '创建自定义材质',
      complexity: 'advanced',
      tags: ['自定义', '材质']
    },
    {
      id: 'material-preset',
      name: '材质预设',
      type: 'MaterialPreset',
      category: RenderingNodeCategory.MATERIAL_EDITING,
      description: '使用材质预设',
      complexity: 'basic',
      tags: ['预设', '材质']
    },
    {
      id: 'material-editor',
      name: '材质编辑器',
      type: 'MaterialEditor',
      category: RenderingNodeCategory.MATERIAL_EDITING,
      description: '可视化材质编辑器',
      complexity: 'intermediate',
      tags: ['编辑器', '可视化']
    },
    {
      id: 'material-preview',
      name: '材质预览',
      type: 'MaterialPreview',
      category: RenderingNodeCategory.MATERIAL_EDITING,
      description: '实时预览材质效果',
      complexity: 'basic',
      tags: ['预览', '实时']
    },
    {
      id: 'material-library',
      name: '材质库',
      type: 'MaterialLibrary',
      category: RenderingNodeCategory.MATERIAL_EDITING,
      description: '管理材质资源库',
      complexity: 'intermediate',
      tags: ['库', '管理']
    }
  ], []);

  // 基础着色器节点（15个）
  const basicShaderNodes: RenderingNodeItem[] = useMemo(() => [
    {
      id: 'vertex-shader',
      name: '顶点着色器',
      type: 'VertexShader',
      category: RenderingNodeCategory.BASIC_SHADER,
      description: '处理顶点变换的着色器',
      complexity: 'intermediate',
      tags: ['顶点', '着色器']
    },
    {
      id: 'fragment-shader',
      name: '片段着色器',
      type: 'FragmentShader',
      category: RenderingNodeCategory.BASIC_SHADER,
      description: '处理像素着色的着色器',
      complexity: 'intermediate',
      tags: ['片段', '像素']
    },
    {
      id: 'compute-shader',
      name: '计算着色器',
      type: 'ComputeShader',
      category: RenderingNodeCategory.BASIC_SHADER,
      description: '执行通用计算的着色器',
      complexity: 'advanced',
      tags: ['计算', '通用']
    },
    {
      id: 'shader-compiler',
      name: '着色器编译器',
      type: 'ShaderCompiler',
      category: RenderingNodeCategory.BASIC_SHADER,
      description: '编译着色器代码',
      complexity: 'advanced',
      tags: ['编译', '代码']
    },
    {
      id: 'shader-optimization',
      name: '着色器优化',
      type: 'ShaderOptimization',
      category: RenderingNodeCategory.BASIC_SHADER,
      description: '优化着色器性能',
      complexity: 'advanced',
      tags: ['优化', '性能']
    },
    {
      id: 'geometry-shader',
      name: '几何着色器',
      type: 'GeometryShader',
      category: RenderingNodeCategory.BASIC_SHADER,
      description: '处理几何变换的着色器',
      complexity: 'advanced',
      tags: ['几何', '变换']
    },
    {
      id: 'shader-linker',
      name: '着色器链接器',
      type: 'ShaderLinker',
      category: RenderingNodeCategory.BASIC_SHADER,
      description: '链接着色器程序',
      complexity: 'intermediate',
      tags: ['链接', '程序']
    },
    {
      id: 'shader-preprocessor',
      name: '着色器预处理器',
      type: 'ShaderPreprocessor',
      category: RenderingNodeCategory.BASIC_SHADER,
      description: '预处理着色器代码',
      complexity: 'intermediate',
      tags: ['预处理', '代码']
    },
    {
      id: 'shader-variant',
      name: '着色器变体',
      type: 'ShaderVariant',
      category: RenderingNodeCategory.BASIC_SHADER,
      description: '管理着色器变体',
      complexity: 'intermediate',
      tags: ['变体', '管理']
    },
    {
      id: 'shader-parameter',
      name: '着色器参数',
      type: 'ShaderParameter',
      category: RenderingNodeCategory.BASIC_SHADER,
      description: '管理着色器参数',
      complexity: 'basic',
      tags: ['参数', '管理']
    },
    {
      id: 'shader-include',
      name: '着色器包含',
      type: 'ShaderInclude',
      category: RenderingNodeCategory.BASIC_SHADER,
      description: '包含着色器代码片段',
      complexity: 'intermediate',
      tags: ['包含', '代码片段']
    },
    {
      id: 'shader-macro',
      name: '着色器宏',
      type: 'ShaderMacro',
      category: RenderingNodeCategory.BASIC_SHADER,
      description: '定义着色器宏',
      complexity: 'intermediate',
      tags: ['宏', '定义']
    },
    {
      id: 'shader-debug',
      name: '着色器调试',
      type: 'ShaderDebug',
      category: RenderingNodeCategory.BASIC_SHADER,
      description: '调试着色器代码',
      complexity: 'advanced',
      tags: ['调试', '代码']
    },
    {
      id: 'shader-performance-analysis',
      name: '着色器性能分析',
      type: 'ShaderPerformanceAnalysis',
      category: RenderingNodeCategory.BASIC_SHADER,
      description: '分析着色器性能',
      complexity: 'advanced',
      tags: ['性能', '分析']
    },
    {
      id: 'shader-validation',
      name: '着色器验证',
      type: 'ShaderValidation',
      category: RenderingNodeCategory.BASIC_SHADER,
      description: '验证着色器正确性',
      complexity: 'intermediate',
      tags: ['验证', '正确性']
    }
  ], []);

  // 高级着色器节点（6个）
  const advancedShaderNodes: RenderingNodeItem[] = useMemo(() => [
    {
      id: 'tessellation-shader',
      name: '细分着色器',
      type: 'TessellationShader',
      category: RenderingNodeCategory.ADVANCED_SHADER,
      description: '执行表面细分的着色器',
      complexity: 'advanced',
      tags: ['细分', '表面']
    },
    {
      id: 'shader-conditional',
      name: '着色器条件',
      type: 'ShaderConditional',
      category: RenderingNodeCategory.ADVANCED_SHADER,
      description: '条件编译着色器',
      complexity: 'advanced',
      tags: ['条件', '编译']
    },
    {
      id: 'shader-loop',
      name: '着色器循环',
      type: 'ShaderLoop',
      category: RenderingNodeCategory.ADVANCED_SHADER,
      description: '在着色器中执行循环',
      complexity: 'advanced',
      tags: ['循环', '执行']
    },
    {
      id: 'custom-shader',
      name: '自定义着色器',
      type: 'CustomShader',
      category: RenderingNodeCategory.ADVANCED_SHADER,
      description: '创建自定义着色器',
      complexity: 'advanced',
      tags: ['自定义', '创建']
    },
    {
      id: 'shader-effect',
      name: '着色器效果',
      type: 'ShaderEffect',
      category: RenderingNodeCategory.ADVANCED_SHADER,
      description: '应用着色器特效',
      complexity: 'advanced',
      tags: ['效果', '特效']
    },
    {
      id: 'shader-template',
      name: '着色器模板',
      type: 'ShaderTemplate',
      category: RenderingNodeCategory.ADVANCED_SHADER,
      description: '使用着色器模板',
      complexity: 'intermediate',
      tags: ['模板', '使用']
    }
  ], []);

  // 光照相机节点（4个）
  const lightingCameraNodes: RenderingNodeItem[] = useMemo(() => [
    {
      id: 'light-control',
      name: '光照控制',
      type: 'LightControl',
      category: RenderingNodeCategory.LIGHTING_CAMERA,
      description: '控制场景光照',
      complexity: 'basic',
      tags: ['光照', '控制']
    },
    {
      id: 'camera-manager',
      name: '相机管理器',
      type: 'CameraManager',
      category: RenderingNodeCategory.LIGHTING_CAMERA,
      description: '管理场景相机',
      complexity: 'intermediate',
      tags: ['相机', '管理']
    },
    {
      id: 'create-camera',
      name: '创建相机',
      type: 'CreateCamera',
      category: RenderingNodeCategory.LIGHTING_CAMERA,
      description: '创建新的相机',
      complexity: 'basic',
      tags: ['创建', '相机']
    },
    {
      id: 'camera-control',
      name: '相机控制',
      type: 'CameraControl',
      category: RenderingNodeCategory.LIGHTING_CAMERA,
      description: '控制相机参数和行为',
      complexity: 'basic',
      tags: ['控制', '参数']
    }
  ], []);

  // 渲染优化节点（15个）
  const renderingOptimizationNodes: RenderingNodeItem[] = useMemo(() => [
    {
      id: 'lod-system',
      name: 'LOD系统',
      type: 'LODSystem',
      category: RenderingNodeCategory.RENDERING_OPTIMIZATION,
      description: '管理细节层次优化',
      complexity: 'intermediate',
      tags: ['LOD', '优化']
    },
    {
      id: 'frustum-culling',
      name: '视锥体剔除',
      type: 'FrustumCulling',
      category: RenderingNodeCategory.RENDERING_OPTIMIZATION,
      description: '剔除视野外对象',
      complexity: 'intermediate',
      tags: ['剔除', '视锥体']
    },
    {
      id: 'occlusion-culling',
      name: '遮挡剔除',
      type: 'OcclusionCulling',
      category: RenderingNodeCategory.RENDERING_OPTIMIZATION,
      description: '剔除被遮挡对象',
      complexity: 'advanced',
      tags: ['遮挡', '剔除']
    },
    {
      id: 'batch-rendering',
      name: '批处理渲染',
      type: 'BatchRendering',
      category: RenderingNodeCategory.RENDERING_OPTIMIZATION,
      description: '合并渲染批次',
      complexity: 'intermediate',
      tags: ['批处理', '合并']
    },
    {
      id: 'instanced-rendering',
      name: '实例化渲染',
      type: 'InstancedRendering',
      category: RenderingNodeCategory.RENDERING_OPTIMIZATION,
      description: '高效渲染重复对象',
      complexity: 'advanced',
      tags: ['实例化', '重复']
    },
    {
      id: 'draw-call-optimization',
      name: '绘制调用优化',
      type: 'DrawCallOptimization',
      category: RenderingNodeCategory.RENDERING_OPTIMIZATION,
      description: '减少绘制调用次数',
      complexity: 'advanced',
      tags: ['绘制调用', '减少']
    },
    {
      id: 'texture-atlas',
      name: '纹理图集',
      type: 'TextureAtlas',
      category: RenderingNodeCategory.RENDERING_OPTIMIZATION,
      description: '合并纹理减少绘制调用',
      complexity: 'intermediate',
      tags: ['纹理', '图集']
    },
    {
      id: 'mesh-combining',
      name: '网格合并',
      type: 'MeshCombining',
      category: RenderingNodeCategory.RENDERING_OPTIMIZATION,
      description: '合并网格优化性能',
      complexity: 'intermediate',
      tags: ['网格', '合并']
    },
    {
      id: 'render-queue',
      name: '渲染队列',
      type: 'RenderQueue',
      category: RenderingNodeCategory.RENDERING_OPTIMIZATION,
      description: '管理渲染顺序',
      complexity: 'intermediate',
      tags: ['队列', '顺序']
    },
    {
      id: 'performance-profiler',
      name: '性能分析器',
      type: 'PerformanceProfiler',
      category: RenderingNodeCategory.RENDERING_OPTIMIZATION,
      description: '分析渲染性能',
      complexity: 'advanced',
      tags: ['性能', '分析']
    },
    {
      id: 'render-statistics',
      name: '渲染统计',
      type: 'RenderStatistics',
      category: RenderingNodeCategory.RENDERING_OPTIMIZATION,
      description: '收集渲染统计信息',
      complexity: 'basic',
      tags: ['统计', '信息']
    },
    {
      id: 'gpu-memory-monitor',
      name: 'GPU内存监控',
      type: 'GPUMemoryMonitor',
      category: RenderingNodeCategory.RENDERING_OPTIMIZATION,
      description: '监控GPU内存使用',
      complexity: 'intermediate',
      tags: ['GPU', '内存']
    },
    {
      id: 'render-pipeline',
      name: '渲染管线',
      type: 'RenderPipeline',
      category: RenderingNodeCategory.RENDERING_OPTIMIZATION,
      description: '管理渲染管线',
      complexity: 'advanced',
      tags: ['管线', '管理']
    },
    {
      id: 'custom-render-pass',
      name: '自定义渲染通道',
      type: 'CustomRenderPass',
      category: RenderingNodeCategory.RENDERING_OPTIMIZATION,
      description: '创建自定义渲染通道',
      complexity: 'advanced',
      tags: ['自定义', '通道']
    },
    {
      id: 'render-target',
      name: '渲染目标',
      type: 'RenderTarget',
      category: RenderingNodeCategory.RENDERING_OPTIMIZATION,
      description: '管理渲染目标',
      complexity: 'intermediate',
      tags: ['目标', '管理']
    }
  ], []);

  // 基础后处理节点（15个）
  const basicPostProcessingNodes: RenderingNodeItem[] = useMemo(() => [
    {
      id: 'bloom-effect',
      name: '辉光效果',
      type: 'BloomEffect',
      category: RenderingNodeCategory.BASIC_POST_PROCESSING,
      description: '添加辉光后处理效果',
      complexity: 'intermediate',
      tags: ['辉光', '效果']
    },
    {
      id: 'blur-effect',
      name: '模糊效果',
      type: 'BlurEffect',
      category: RenderingNodeCategory.BASIC_POST_PROCESSING,
      description: '添加模糊后处理效果',
      complexity: 'basic',
      tags: ['模糊', '效果']
    },
    {
      id: 'color-grading',
      name: '色彩分级',
      type: 'ColorGrading',
      category: RenderingNodeCategory.BASIC_POST_PROCESSING,
      description: '调整图像色彩',
      complexity: 'intermediate',
      tags: ['色彩', '分级']
    },
    {
      id: 'tone-mapping',
      name: '色调映射',
      type: 'ToneMapping',
      category: RenderingNodeCategory.BASIC_POST_PROCESSING,
      description: '执行HDR色调映射',
      complexity: 'intermediate',
      tags: ['色调', 'HDR']
    },
    {
      id: 'ssao',
      name: '屏幕空间环境光遮蔽',
      type: 'SSAO',
      category: RenderingNodeCategory.BASIC_POST_PROCESSING,
      description: '添加环境光遮蔽效果',
      complexity: 'advanced',
      tags: ['SSAO', '遮蔽']
    },
    {
      id: 'ssr',
      name: '屏幕空间反射',
      type: 'SSR',
      category: RenderingNodeCategory.BASIC_POST_PROCESSING,
      description: '添加屏幕空间反射',
      complexity: 'advanced',
      tags: ['SSR', '反射']
    },
    {
      id: 'fxaa',
      name: 'FXAA抗锯齿',
      type: 'FXAA',
      category: RenderingNodeCategory.BASIC_POST_PROCESSING,
      description: '快速近似抗锯齿',
      complexity: 'intermediate',
      tags: ['FXAA', '抗锯齿']
    },
    {
      id: 'taa',
      name: 'TAA抗锯齿',
      type: 'TAA',
      category: RenderingNodeCategory.BASIC_POST_PROCESSING,
      description: '时间抗锯齿',
      complexity: 'advanced',
      tags: ['TAA', '时间']
    },
    {
      id: 'depth-of-field',
      name: '景深效果',
      type: 'DepthOfField',
      category: RenderingNodeCategory.BASIC_POST_PROCESSING,
      description: '模拟相机景深效果',
      complexity: 'intermediate',
      tags: ['景深', '相机']
    },
    {
      id: 'motion-blur',
      name: '运动模糊',
      type: 'MotionBlur',
      category: RenderingNodeCategory.BASIC_POST_PROCESSING,
      description: '添加运动模糊效果',
      complexity: 'intermediate',
      tags: ['运动', '模糊']
    },
    {
      id: 'vignette',
      name: '暗角效果',
      type: 'Vignette',
      category: RenderingNodeCategory.BASIC_POST_PROCESSING,
      description: '添加镜头暗角效果',
      complexity: 'basic',
      tags: ['暗角', '镜头']
    },
    {
      id: 'film-grain',
      name: '胶片颗粒',
      type: 'FilmGrain',
      category: RenderingNodeCategory.BASIC_POST_PROCESSING,
      description: '添加胶片颗粒效果',
      complexity: 'basic',
      tags: ['胶片', '颗粒']
    },
    {
      id: 'lens-distortion',
      name: '镜头畸变',
      type: 'LensDistortion',
      category: RenderingNodeCategory.BASIC_POST_PROCESSING,
      description: '模拟镜头畸变效果',
      complexity: 'intermediate',
      tags: ['镜头', '畸变']
    },
    {
      id: 'outline-effect',
      name: '轮廓效果',
      type: 'OutlineEffect',
      category: RenderingNodeCategory.BASIC_POST_PROCESSING,
      description: '为对象添加轮廓线',
      complexity: 'intermediate',
      tags: ['轮廓', '线条']
    },
    {
      id: 'glow-effect',
      name: '发光效果',
      type: 'GlowEffect',
      category: RenderingNodeCategory.BASIC_POST_PROCESSING,
      description: '为对象添加发光效果',
      complexity: 'intermediate',
      tags: ['发光', '效果']
    }
  ], []);

  // 高级后处理节点（11个）
  const advancedPostProcessingNodes: RenderingNodeItem[] = useMemo(() => [
    {
      id: 'chromatic-aberration',
      name: '色差效果',
      type: 'ChromaticAberration',
      category: RenderingNodeCategory.ADVANCED_POST_PROCESSING,
      description: '模拟镜头色差',
      complexity: 'intermediate',
      tags: ['色差', '镜头']
    },
    {
      id: 'edge-detection',
      name: '边缘检测',
      type: 'EdgeDetection',
      category: RenderingNodeCategory.ADVANCED_POST_PROCESSING,
      description: '检测图像边缘',
      complexity: 'intermediate',
      tags: ['边缘', '检测']
    },
    {
      id: 'sharpen-filter',
      name: '锐化滤镜',
      type: 'SharpenFilter',
      category: RenderingNodeCategory.ADVANCED_POST_PROCESSING,
      description: '锐化图像细节',
      complexity: 'basic',
      tags: ['锐化', '细节']
    },
    {
      id: 'contrast-adjustment',
      name: '对比度调整',
      type: 'ContrastAdjustment',
      category: RenderingNodeCategory.ADVANCED_POST_PROCESSING,
      description: '调整图像对比度',
      complexity: 'basic',
      tags: ['对比度', '调整']
    },
    {
      id: 'brightness-adjustment',
      name: '亮度调整',
      type: 'BrightnessAdjustment',
      category: RenderingNodeCategory.ADVANCED_POST_PROCESSING,
      description: '调整图像亮度',
      complexity: 'basic',
      tags: ['亮度', '调整']
    },
    {
      id: 'saturation-adjustment',
      name: '饱和度调整',
      type: 'SaturationAdjustment',
      category: RenderingNodeCategory.ADVANCED_POST_PROCESSING,
      description: '调整图像饱和度',
      complexity: 'basic',
      tags: ['饱和度', '调整']
    },
    {
      id: 'hue-shift',
      name: '色相偏移',
      type: 'HueShift',
      category: RenderingNodeCategory.ADVANCED_POST_PROCESSING,
      description: '偏移图像色相',
      complexity: 'basic',
      tags: ['色相', '偏移']
    },
    {
      id: 'invert-color',
      name: '反色效果',
      type: 'InvertColor',
      category: RenderingNodeCategory.ADVANCED_POST_PROCESSING,
      description: '反转图像颜色',
      complexity: 'basic',
      tags: ['反色', '颜色']
    },
    {
      id: 'grayscale',
      name: '灰度效果',
      type: 'Grayscale',
      category: RenderingNodeCategory.ADVANCED_POST_PROCESSING,
      description: '将图像转为灰度',
      complexity: 'basic',
      tags: ['灰度', '转换']
    },
    {
      id: 'sepia-effect',
      name: '棕褐色效果',
      type: 'SepiaEffect',
      category: RenderingNodeCategory.ADVANCED_POST_PROCESSING,
      description: '添加复古棕褐色效果',
      complexity: 'basic',
      tags: ['棕褐色', '复古']
    },
    {
      id: 'custom-post-process',
      name: '自定义后处理',
      type: 'CustomPostProcess',
      category: RenderingNodeCategory.ADVANCED_POST_PROCESSING,
      description: '创建自定义后处理效果',
      complexity: 'advanced',
      tags: ['自定义', '后处理']
    }
  ], []);

  // 合并所有节点
  const allNodes = useMemo(() => [
    ...materialEditingNodes,
    ...basicShaderNodes,
    ...advancedShaderNodes,
    ...lightingCameraNodes,
    ...renderingOptimizationNodes,
    ...basicPostProcessingNodes,
    ...advancedPostProcessingNodes
  ], [
    materialEditingNodes,
    basicShaderNodes,
    advancedShaderNodes,
    lightingCameraNodes,
    renderingOptimizationNodes,
    basicPostProcessingNodes,
    advancedPostProcessingNodes
  ]);

  // 过滤节点
  const filteredNodesByCategory = useMemo(() => {
    let nodes = allNodes;

    // 按分类过滤
    if (selectedCategory !== 'all') {
      nodes = nodes.filter(node => node.category === selectedCategory);
    }

    // 按搜索文本过滤
    if (searchText) {
      const searchLower = searchText.toLowerCase();
      nodes = nodes.filter(node =>
        node.name.toLowerCase().includes(searchLower) ||
        node.description.toLowerCase().includes(searchLower) ||
        node.tags?.some(tag => tag.toLowerCase().includes(searchLower))
      );
    }

    return nodes;
  }, [allNodes, selectedCategory, searchText]);

  // 按分类分组节点
  const nodesByCategory = useMemo(() => {
    const grouped: Record<RenderingNodeCategory, RenderingNodeItem[]> = {
      [RenderingNodeCategory.MATERIAL_EDITING]: [],
      [RenderingNodeCategory.BASIC_SHADER]: [],
      [RenderingNodeCategory.ADVANCED_SHADER]: [],
      [RenderingNodeCategory.LIGHTING_CAMERA]: [],
      [RenderingNodeCategory.RENDERING_OPTIMIZATION]: [],
      [RenderingNodeCategory.BASIC_POST_PROCESSING]: [],
      [RenderingNodeCategory.ADVANCED_POST_PROCESSING]: []
    };

    filteredNodesByCategory.forEach(node => {
      grouped[node.category].push(node);
    });

    return grouped;
  }, [filteredNodesByCategory]);

  // 渲染节点列表项
  const renderNodeItem = useCallback((node: RenderingNodeItem) => (
    <DraggableRenderingNode
      key={node.id}
      node={node}
      onNodeSelect={onNodeSelect}
      onNodeAdd={onNodeAdd}
      onDragStart={(dragNode) => {
        console.log('开始拖拽节点:', dragNode.name);
      }}
      onDragEnd={(dragNode, didDrop) => {
        console.log('拖拽结束:', dragNode.name, '是否放置:', didDrop);
      }}
    />
  ), [onNodeSelect, onNodeAdd]);

  if (!visible) {
    return null;
  }

  return (
    <Card
      title={
        <Space>
          <BgColorsOutlined />
          <Title level={4} style={{ margin: 0 }}>
            核心渲染系统节点
          </Title>
          <Badge count={80} style={{ backgroundColor: '#52c41a' }} />
        </Space>
      }
      size="small"
      style={{ height, width, overflow: 'hidden' }}
      styles={{ body: { padding: '12px', height: 'calc(100% - 57px)', overflow: 'auto' } }}
    >
      <Spin spinning={loading}>
        {/* 搜索和过滤 */}
        <Space direction="vertical" style={{ width: '100%', marginBottom: 12 }}>
          <Search
            placeholder="搜索渲染节点..."
            allowClear
            onSearch={setSearchText}
            onChange={(e) => setSearchText(e.target.value)}
            prefix={<SearchOutlined />}
          />

          <Select
            style={{ width: '100%' }}
            placeholder="选择分类"
            value={selectedCategory}
            onChange={setSelectedCategory}
          >
            <Option value="all">全部分类</Option>
            {Object.entries(RENDERING_CATEGORY_MAP).map(([key, category]) => (
              <Option key={key} value={key}>
                {category.icon} {category.name}
              </Option>
            ))}
          </Select>
        </Space>

        <Divider style={{ margin: '8px 0' }} />

        {/* 节点分类面板 */}
        <Collapse
          activeKey={expandedPanels}
          onChange={(keys) => setExpandedPanels(keys as string[])}
          size="small"
          ghost
        >
          {/* 材质编辑面板 */}
          <Panel
            header={
              <Space>
                {RENDERING_CATEGORY_MAP[RenderingNodeCategory.MATERIAL_EDITING].icon}
                <Text strong>材质编辑</Text>
                <Badge count={nodesByCategory[RenderingNodeCategory.MATERIAL_EDITING].length} size="small" />
              </Space>
            }
            key={RenderingNodeCategory.MATERIAL_EDITING}
          >
            <div>
              {nodesByCategory[RenderingNodeCategory.MATERIAL_EDITING].map(renderNodeItem)}
            </div>
          </Panel>

          {/* 基础着色器面板 */}
          <Panel
            header={
              <Space>
                {RENDERING_CATEGORY_MAP[RenderingNodeCategory.BASIC_SHADER].icon}
                <Text strong>基础着色器</Text>
                <Badge count={nodesByCategory[RenderingNodeCategory.BASIC_SHADER].length} size="small" />
              </Space>
            }
            key={RenderingNodeCategory.BASIC_SHADER}
          >
            <div>
              {nodesByCategory[RenderingNodeCategory.BASIC_SHADER].map(renderNodeItem)}
            </div>
          </Panel>

          {/* 高级着色器面板 */}
          <Panel
            header={
              <Space>
                {RENDERING_CATEGORY_MAP[RenderingNodeCategory.ADVANCED_SHADER].icon}
                <Text strong>高级着色器</Text>
                <Badge count={nodesByCategory[RenderingNodeCategory.ADVANCED_SHADER].length} size="small" />
              </Space>
            }
            key={RenderingNodeCategory.ADVANCED_SHADER}
          >
            <div>
              {nodesByCategory[RenderingNodeCategory.ADVANCED_SHADER].map(renderNodeItem)}
            </div>
          </Panel>

          {/* 光照相机面板 */}
          <Panel
            header={
              <Space>
                {RENDERING_CATEGORY_MAP[RenderingNodeCategory.LIGHTING_CAMERA].icon}
                <Text strong>光照相机</Text>
                <Badge count={nodesByCategory[RenderingNodeCategory.LIGHTING_CAMERA].length} size="small" />
              </Space>
            }
            key={RenderingNodeCategory.LIGHTING_CAMERA}
          >
            <div>
              {nodesByCategory[RenderingNodeCategory.LIGHTING_CAMERA].map(renderNodeItem)}
            </div>
          </Panel>

          {/* 渲染优化面板 */}
          <Panel
            header={
              <Space>
                {RENDERING_CATEGORY_MAP[RenderingNodeCategory.RENDERING_OPTIMIZATION].icon}
                <Text strong>渲染优化</Text>
                <Badge count={nodesByCategory[RenderingNodeCategory.RENDERING_OPTIMIZATION].length} size="small" />
              </Space>
            }
            key={RenderingNodeCategory.RENDERING_OPTIMIZATION}
          >
            <div>
              {nodesByCategory[RenderingNodeCategory.RENDERING_OPTIMIZATION].map(renderNodeItem)}
            </div>
          </Panel>

          {/* 基础后处理面板 */}
          <Panel
            header={
              <Space>
                {RENDERING_CATEGORY_MAP[RenderingNodeCategory.BASIC_POST_PROCESSING].icon}
                <Text strong>基础后处理</Text>
                <Badge count={nodesByCategory[RenderingNodeCategory.BASIC_POST_PROCESSING].length} size="small" />
              </Space>
            }
            key={RenderingNodeCategory.BASIC_POST_PROCESSING}
          >
            <div>
              {nodesByCategory[RenderingNodeCategory.BASIC_POST_PROCESSING].map(renderNodeItem)}
            </div>
          </Panel>

          {/* 高级后处理面板 */}
          <Panel
            header={
              <Space>
                {RENDERING_CATEGORY_MAP[RenderingNodeCategory.ADVANCED_POST_PROCESSING].icon}
                <Text strong>高级后处理</Text>
                <Badge count={nodesByCategory[RenderingNodeCategory.ADVANCED_POST_PROCESSING].length} size="small" />
              </Space>
            }
            key={RenderingNodeCategory.ADVANCED_POST_PROCESSING}
          >
            <div>
              {nodesByCategory[RenderingNodeCategory.ADVANCED_POST_PROCESSING].map(renderNodeItem)}
            </div>
          </Panel>
        </Collapse>
      </Spin>
    </Card>
  );
};

export default RenderingNodesPanel;
