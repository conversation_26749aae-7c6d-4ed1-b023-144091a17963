/**
 * 核心渲染系统节点拖拽组件
 * 实现节点从面板拖拽到编辑器画布的功能
 */

import React, { useCallback, useState } from 'react';
import { useDrag, useDrop } from 'react-dnd';
import { Card, Tag, Space, Typography, Tooltip, Button } from 'antd';
import { 
  PlusOutlined, 
  DragOutlined, 
  EyeOutlined,
  InfoCircleOutlined 
} from '@ant-design/icons';
import { RenderingNodeItem } from '../panels/RenderingNodesPanel';

const { Text } = Typography;

// 拖拽项目类型
export const ItemTypes = {
  RENDERING_NODE: 'rendering_node'
};

// 拖拽数据接口
export interface DragItem {
  type: string;
  node: RenderingNodeItem;
}

// 拖拽预览组件属性
interface NodeDragPreviewProps {
  node: RenderingNodeItem;
  isDragging: boolean;
}

/**
 * 节点拖拽预览组件
 */
const NodeDragPreview: React.FC<NodeDragPreviewProps> = ({ node, isDragging }) => {
  return (
    <Card
      size="small"
      style={{
        width: 200,
        opacity: isDragging ? 0.5 : 1,
        cursor: 'move',
        border: '2px dashed #1890ff',
        backgroundColor: '#f0f8ff'
      }}
    >
      <Space direction="vertical" size="small" style={{ width: '100%' }}>
        <Space>
          <DragOutlined />
          <Text strong>{node.name}</Text>
        </Space>
        <Text type="secondary" style={{ fontSize: '12px' }}>
          {node.description}
        </Text>
        <Space>
          <Tag color={
            node.complexity === 'basic' ? 'green' : 
            node.complexity === 'intermediate' ? 'orange' : 'red'
          }>
            {node.complexity}
          </Tag>
          {node.tags?.slice(0, 2).map(tag => (
            <Tag key={tag}>{tag}</Tag>
          ))}
        </Space>
      </Space>
    </Card>
  );
};

// 可拖拽节点组件属性
interface DraggableRenderingNodeProps {
  node: RenderingNodeItem;
  onNodeSelect?: (node: RenderingNodeItem) => void;
  onNodeAdd?: (nodeType: string) => void;
  onDragStart?: (node: RenderingNodeItem) => void;
  onDragEnd?: (node: RenderingNodeItem, didDrop: boolean) => void;
}

/**
 * 可拖拽的渲染节点组件
 */
export const DraggableRenderingNode: React.FC<DraggableRenderingNodeProps> = ({
  node,
  onNodeSelect,
  onNodeAdd,
  onDragStart,
  onDragEnd
}) => {
  const [isHovered, setIsHovered] = useState(false);

  // 配置拖拽
  const [{ isDragging }, drag, preview] = useDrag({
    type: ItemTypes.RENDERING_NODE,
    item: (): DragItem => {
      onDragStart?.(node);
      return { type: ItemTypes.RENDERING_NODE, node };
    },
    end: (item, monitor) => {
      const didDrop = monitor.didDrop();
      onDragEnd?.(node, didDrop);
    },
    collect: (monitor) => ({
      isDragging: monitor.isDragging()
    })
  });

  // 处理节点点击
  const handleNodeClick = useCallback(() => {
    onNodeSelect?.(node);
  }, [node, onNodeSelect]);

  // 处理添加按钮点击
  const handleAddClick = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    onNodeAdd?.(node.type);
  }, [node.type, onNodeAdd]);

  // 处理预览按钮点击
  const handlePreviewClick = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    // 这里可以添加节点预览逻辑
    console.log('预览节点:', node);
  }, [node]);

  return (
    <>
      {/* 拖拽预览 */}
      <div ref={preview} style={{ display: 'none' }}>
        <NodeDragPreview node={node} isDragging={isDragging} />
      </div>

      {/* 实际的节点项 */}
      <div
        ref={drag}
        style={{
          opacity: isDragging ? 0.5 : 1,
          cursor: 'move',
          padding: '8px',
          margin: '4px 0',
          border: isHovered ? '1px solid #1890ff' : '1px solid #d9d9d9',
          borderRadius: '6px',
          backgroundColor: isHovered ? '#f0f8ff' : '#ffffff',
          transition: 'all 0.2s ease'
        }}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        onClick={handleNodeClick}
      >
        <Space direction="vertical" size="small" style={{ width: '100%' }}>
          {/* 节点标题行 */}
          <Space style={{ width: '100%', justifyContent: 'space-between' }}>
            <Space>
              <DragOutlined style={{ color: '#8c8c8c' }} />
              <Text strong>{node.name}</Text>
              <Tag color={
                node.complexity === 'basic' ? 'green' : 
                node.complexity === 'intermediate' ? 'orange' : 'red'
              }>
                {node.complexity}
              </Tag>
            </Space>
            
            <Space>
              <Tooltip title="预览节点">
                <Button
                  type="text"
                  size="small"
                  icon={<EyeOutlined />}
                  onClick={handlePreviewClick}
                />
              </Tooltip>
              <Tooltip title="添加到画布">
                <Button
                  type="text"
                  size="small"
                  icon={<PlusOutlined />}
                  onClick={handleAddClick}
                />
              </Tooltip>
            </Space>
          </Space>

          {/* 节点描述 */}
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {node.description}
          </Text>

          {/* 节点标签 */}
          {node.tags && node.tags.length > 0 && (
            <Space wrap>
              {node.tags.slice(0, 3).map(tag => (
                <Tag key={tag}>{tag}</Tag>
              ))}
              {node.tags.length > 3 && (
                <Tooltip title={node.tags.slice(3).join(', ')}>
                  <Tag>+{node.tags.length - 3}</Tag>
                </Tooltip>
              )}
            </Space>
          )}

          {/* 节点输入输出信息 */}
          {(node.inputs || node.outputs) && (
            <Space>
              {node.inputs && (
                <Tooltip title={`输入: ${node.inputs.map(i => i.name).join(', ')}`}>
                  <Tag color="blue">输入: {node.inputs.length}</Tag>
                </Tooltip>
              )}
              {node.outputs && (
                <Tooltip title={`输出: ${node.outputs.map(o => o.name).join(', ')}`}>
                  <Tag color="green">输出: {node.outputs.length}</Tag>
                </Tooltip>
              )}
            </Space>
          )}
        </Space>
      </div>
    </>
  );
};

// 拖拽目标区域属性
interface RenderingNodeDropZoneProps {
  onNodeDrop?: (node: RenderingNodeItem, position: { x: number; y: number }) => void;
  children?: React.ReactNode;
  style?: React.CSSProperties;
}

/**
 * 渲染节点拖拽目标区域
 */
export const RenderingNodeDropZone: React.FC<RenderingNodeDropZoneProps> = ({
  onNodeDrop,
  children,
  style
}) => {
  const [{ isOver, canDrop }, drop] = useDrop({
    accept: ItemTypes.RENDERING_NODE,
    drop: (item: DragItem, monitor) => {
      const clientOffset = monitor.getClientOffset();
      if (clientOffset && onNodeDrop) {
        const position = {
          x: clientOffset.x,
          y: clientOffset.y
        };
        onNodeDrop(item.node, position);
      }
    },
    collect: (monitor) => ({
      isOver: monitor.isOver(),
      canDrop: monitor.canDrop()
    })
  });

  const dropZoneStyle: React.CSSProperties = {
    ...style,
    backgroundColor: isOver && canDrop ? '#e6f7ff' : 'transparent',
    border: isOver && canDrop ? '2px dashed #1890ff' : 'none',
    transition: 'all 0.2s ease'
  };

  return (
    <div ref={drop} style={dropZoneStyle}>
      {children}
      {isOver && canDrop && (
        <div
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(24, 144, 255, 0.1)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            pointerEvents: 'none',
            zIndex: 1000
          }}
        >
          <Text style={{ fontSize: '16px', color: '#1890ff' }}>
            释放以添加节点
          </Text>
        </div>
      )}
    </div>
  );
};

export default DraggableRenderingNode;
