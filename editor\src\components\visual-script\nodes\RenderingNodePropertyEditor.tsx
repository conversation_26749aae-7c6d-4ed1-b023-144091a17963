/**
 * 核心渲染系统节点属性编辑器
 * 为每个渲染系统节点提供专门的属性编辑界面，支持实时预览和参数调整
 */

import React, { useState, useEffect, useCallback } from 'react';
import {
  Card,
  Form,
  Input,
  InputNumber,
  Select,
  Switch,
  Slider,
  ColorPicker,
  Space,
  Button,
  Divider,
  Typography,
  Tabs,
  Row,
  Col,
  Tooltip,
  Alert,
  Badge
} from 'antd';
import {
  SettingOutlined,
  EyeOutlined,
  ReloadOutlined,
  SaveOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import { RenderingNodeItem, RenderingNodeCategory } from '../panels/RenderingNodesPanel';

const { Text, Title } = Typography;
const { Option } = Select;
const { TabPane } = Tabs;

// 属性类型定义
export interface NodeProperty {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'color' | 'select' | 'range' | 'vector3' | 'texture';
  value: any;
  defaultValue: any;
  label: string;
  description?: string;
  min?: number;
  max?: number;
  step?: number;
  options?: Array<{ label: string; value: any }>;
  required?: boolean;
}

// 节点属性编辑器组件属性
interface RenderingNodePropertyEditorProps {
  node: RenderingNodeItem;
  properties?: NodeProperty[];
  onPropertyChange?: (propertyName: string, value: any) => void;
  onPreview?: (node: RenderingNodeItem, properties: Record<string, any>) => void;
  onSave?: (node: RenderingNodeItem, properties: Record<string, any>) => void;
  onReset?: (node: RenderingNodeItem) => void;
  visible?: boolean;
}

/**
 * 核心渲染系统节点属性编辑器组件
 */
const RenderingNodePropertyEditor: React.FC<RenderingNodePropertyEditorProps> = ({
  node,
  properties = [],
  onPropertyChange,
  onPreview,
  onSave,
  onReset,
  visible = true
}) => {
  const [form] = Form.useForm();
  const [currentProperties, setCurrentProperties] = useState<Record<string, any>>({});
  const [hasChanges, setHasChanges] = useState(false);
  const [previewEnabled, setPreviewEnabled] = useState(true);

  // 初始化属性值
  useEffect(() => {
    const initialProperties: Record<string, any> = {};
    properties.forEach(prop => {
      initialProperties[prop.name] = prop.value ?? prop.defaultValue;
    });
    setCurrentProperties(initialProperties);
    form.setFieldsValue(initialProperties);
    setHasChanges(false);
  }, [node, properties, form]);

  // 处理属性变化
  const handlePropertyChange = useCallback((propertyName: string, value: any) => {
    const newProperties = { ...currentProperties, [propertyName]: value };
    setCurrentProperties(newProperties);
    setHasChanges(true);
    
    // 通知外部组件
    onPropertyChange?.(propertyName, value);
    
    // 如果启用了实时预览，触发预览
    if (previewEnabled) {
      onPreview?.(node, newProperties);
    }
  }, [currentProperties, node, onPropertyChange, onPreview, previewEnabled]);

  // 处理保存
  const handleSave = useCallback(() => {
    onSave?.(node, currentProperties);
    setHasChanges(false);
  }, [node, currentProperties, onSave]);

  // 处理重置
  const handleReset = useCallback(() => {
    const resetProperties: Record<string, any> = {};
    properties.forEach(prop => {
      resetProperties[prop.name] = prop.defaultValue;
    });
    setCurrentProperties(resetProperties);
    form.setFieldsValue(resetProperties);
    setHasChanges(false);
    onReset?.(node);
  }, [node, properties, form, onReset]);

  // 处理预览
  const handlePreview = useCallback(() => {
    onPreview?.(node, currentProperties);
  }, [node, currentProperties, onPreview]);

  // 渲染属性编辑器
  const renderPropertyEditor = (property: NodeProperty) => {
    const { name, type, label, description, min, max, step, options, required } = property;
    const value = currentProperties[name];

    const commonProps = {
      value,
      onChange: (val: any) => handlePropertyChange(name, val),
      placeholder: description
    };

    let editor;

    switch (type) {
      case 'string':
        editor = <Input {...commonProps} />;
        break;

      case 'number':
        editor = (
          <InputNumber
            {...commonProps}
            min={min}
            max={max}
            step={step}
            style={{ width: '100%' }}
          />
        );
        break;

      case 'boolean':
        editor = (
          <Switch
            checked={value}
            onChange={(checked) => handlePropertyChange(name, checked)}
          />
        );
        break;

      case 'color':
        editor = (
          <ColorPicker
            value={value}
            onChange={(color) => handlePropertyChange(name, color.toHexString())}
            showText
          />
        );
        break;

      case 'select':
        editor = (
          <Select {...commonProps} style={{ width: '100%' }}>
            {options?.map(option => (
              <Option key={option.value} value={option.value}>
                {option.label}
              </Option>
            ))}
          </Select>
        );
        break;

      case 'range':
        editor = (
          <Slider
            value={value}
            onChange={(val) => handlePropertyChange(name, val)}
            min={min}
            max={max}
            step={step}
            marks={min !== undefined && max !== undefined ? {
              [min]: min.toString(),
              [max]: max.toString()
            } : undefined}
          />
        );
        break;

      case 'vector3':
        const vector = value || { x: 0, y: 0, z: 0 };
        editor = (
          <Row gutter={8}>
            <Col span={8}>
              <InputNumber
                placeholder="X"
                value={vector.x}
                onChange={(x) => handlePropertyChange(name, { ...vector, x })}
                style={{ width: '100%' }}
              />
            </Col>
            <Col span={8}>
              <InputNumber
                placeholder="Y"
                value={vector.y}
                onChange={(y) => handlePropertyChange(name, { ...vector, y })}
                style={{ width: '100%' }}
              />
            </Col>
            <Col span={8}>
              <InputNumber
                placeholder="Z"
                value={vector.z}
                onChange={(z) => handlePropertyChange(name, { ...vector, z })}
                style={{ width: '100%' }}
              />
            </Col>
          </Row>
        );
        break;

      case 'texture':
        editor = (
          <Space direction="vertical" style={{ width: '100%' }}>
            <Input
              {...commonProps}
              placeholder="纹理路径或URL"
            />
            <Button size="small" type="dashed" style={{ width: '100%' }}>
              选择纹理文件
            </Button>
          </Space>
        );
        break;

      default:
        editor = <Input {...commonProps} />;
    }

    return (
      <Form.Item
        key={name}
        label={
          <Space>
            <Text>{label}</Text>
            {required && <Text type="danger">*</Text>}
            {description && (
              <Tooltip title={description}>
                <InfoCircleOutlined style={{ color: '#8c8c8c' }} />
              </Tooltip>
            )}
          </Space>
        }
        name={name}
        rules={required ? [{ required: true, message: `请输入${label}` }] : undefined}
      >
        {editor}
      </Form.Item>
    );
  };

  // 按分类分组属性
  const groupedProperties = properties.reduce((groups, property) => {
    const category = getPropertyCategory(property.name);
    if (!groups[category]) {
      groups[category] = [];
    }
    groups[category].push(property);
    return groups;
  }, {} as Record<string, NodeProperty[]>);

  if (!visible) {
    return null;
  }

  return (
    <Card
      title={
        <Space>
          <SettingOutlined />
          <Title level={5} style={{ margin: 0 }}>
            {node.name} - 属性编辑器
          </Title>
          {hasChanges && <Badge status="processing" text="有未保存的更改" />}
        </Space>
      }
      size="small"
      extra={
        <Space>
          <Tooltip title="实时预览">
            <Switch
              checked={previewEnabled}
              onChange={setPreviewEnabled}
              checkedChildren={<EyeOutlined />}
              unCheckedChildren={<EyeOutlined />}
            />
          </Tooltip>
          <Button
            type="text"
            icon={<EyeOutlined />}
            onClick={handlePreview}
            disabled={!onPreview}
          >
            预览
          </Button>
          <Button
            type="text"
            icon={<ReloadOutlined />}
            onClick={handleReset}
            disabled={!hasChanges}
          >
            重置
          </Button>
          <Button
            type="primary"
            icon={<SaveOutlined />}
            onClick={handleSave}
            disabled={!hasChanges || !onSave}
          >
            保存
          </Button>
        </Space>
      }
    >
      {/* 节点信息 */}
      <Alert
        message={`节点类型: ${node.type}`}
        description={node.description}
        type="info"
        showIcon
        style={{ marginBottom: 16 }}
      />

      {/* 属性编辑表单 */}
      <Form
        form={form}
        layout="vertical"
        size="small"
      >
        {Object.keys(groupedProperties).length > 1 ? (
          <Tabs size="small">
            {Object.entries(groupedProperties).map(([category, props]) => (
              <TabPane tab={category} key={category}>
                {props.map(renderPropertyEditor)}
              </TabPane>
            ))}
          </Tabs>
        ) : (
          properties.map(renderPropertyEditor)
        )}
      </Form>
    </Card>
  );
};

/**
 * 根据属性名称获取分类
 */
function getPropertyCategory(propertyName: string): string {
  if (propertyName.includes('color') || propertyName.includes('Color')) {
    return '颜色';
  }
  if (propertyName.includes('position') || propertyName.includes('Position') || 
      propertyName.includes('rotation') || propertyName.includes('Rotation') ||
      propertyName.includes('scale') || propertyName.includes('Scale')) {
    return '变换';
  }
  if (propertyName.includes('texture') || propertyName.includes('Texture') ||
      propertyName.includes('material') || propertyName.includes('Material')) {
    return '材质';
  }
  if (propertyName.includes('light') || propertyName.includes('Light') ||
      propertyName.includes('shadow') || propertyName.includes('Shadow')) {
    return '光照';
  }
  return '基础';
}

export default RenderingNodePropertyEditor;
